import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, TextField } from "@radix-ui/themes";

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export const SearchFilters = ({
  searchQuery,
  onSearchChange,
}: SearchFiltersProps) => {
  return (
    <div className="flex flex gap-4 md:max-w-[400px]">
      <TextField.Root
        className="flex-1"
        placeholder="Search for portfolio ID, email address"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        size="2"
      >
        <TextField.Slot side="left">
          <MagnifyingGlassIcon height="16" width="16" />
        </TextField.Slot>
      </TextField.Root>

      <Button
        radius="full"
        color="gray"
        highContrast
        variant="soft"
        size="2"
        onClick={() => onSearchChange("")}
      >
        Search
      </Button>
    </div>
  );
};
