import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { UserSummaryResponse } from "@/api/data-contracts";
import { Text } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";
import { AmountDisplay } from "@repo/ui/amount-display";

const columnHelper = createColumnHelper<UserSummaryResponse>();

export const useUsersColumns = () => {
  const columns: ColumnDef<UserSummaryResponse, any>[] = [
    columnHelper.accessor("publicId", {
      header: "Portfolio ID",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text size="3" color="orange" className="text-nowrap">
              {value || "-"}
            </Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("email", {
      header: "Email",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text size="3">{value || "-"}</Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("createdAt", {
      header: "Account creation time",
      cell: (info) => {
        const value = info.getValue();
        if (!value) return <Text size="3">-</Text>;

        const date = new Date(value);
        const formattedDate = date.toLocaleString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        });

        return <Text size="3">{formattedDate}</Text>;
      },
      size: 200,
    }),
    columnHelper.accessor("totalInvestment", {
      header: "Total invested amount (USD)",
      cell: (info) => {
        const value = info.getValue();
        return <AmountDisplay amount={value?.amount} />;
      },
      size: 250,
    }),
    columnHelper.accessor("countryOfResidence", {
      header: "Country of residence",
      cell: (info) => {
        const value = info.getValue();
        return <Text size="3">{value || "-"}</Text>;
      },
      size: 180,
    }),
  ];

  return columns;
};
