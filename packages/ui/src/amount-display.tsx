import { Text, TextProps } from "@radix-ui/themes";

export const AmountDisplay = ({
  amount,
  size,
  weight = "medium",
}: {
  amount?: number;
  size: TextProps["size"];
  weight: TextProps["weight"];
}) => {
  if (amount == undefined) return "-";

  const textProps = { size, weight };

  if (amount > 0) {
    return (
      <Text color="green" {...textProps}>
        +{amount.toLocaleString()}
      </Text>
    );
  }

  if (amount < 0) {
    <Text color="red" {...textProps}>
      -{amount.toLocaleString()}
    </Text>;
  }

  return (
    <Text color="gray" {...textProps}>
      {amount.toLocaleString()}
    </Text>
  );
};
